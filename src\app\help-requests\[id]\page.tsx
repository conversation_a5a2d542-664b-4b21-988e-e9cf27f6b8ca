"use client";

import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeftIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { useToast } from "@/components/ToastProvider";
import { getHelpRequestDetail } from "@/services/helpRequestService";
import {
  type HelpRequestDetail,
  type HelpRequestAnswer,
} from "@/types/help-request";

// 状态颜色映射
const getStatusColor = (status: string) => {
  switch (status) {
    case "open":
      return "bg-green-500";
    case "resolved":
      return "bg-blue-500";
    case "closed":
      return "bg-gray-500";
    default:
      return "bg-gray-500";
  }
};

// 状态文本映射
const getStatusText = (status: string) => {
  switch (status) {
    case "open":
      return "求助中";
    case "resolved":
      return "已解决";
    case "closed":
      return "已关闭";
    default:
      return "未知";
  }
};

// 格式化时间
const formatDateTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 回答卡片组件
function AnswerCard({ answer }: { answer: HelpRequestAnswer }) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      {/* 回答者信息 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-medium text-blue-600 dark:text-blue-400">
                {answer.answerer.nickname || answer.answerer.username}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {answer.answerer.points} 积分
              </span>
              {answer.answerer.title && (
                <span className="px-2 py-0.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded text-xs">
                  {answer.answerer.title}
                </span>
              )}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {formatDateTime(answer.created_at)}
            </div>
          </div>
        </div>
        {answer.is_accepted && (
          <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
            <CheckCircleIcon className="w-5 h-5" />
            <span className="text-sm font-medium">已采纳</span>
          </div>
        )}
      </div>

      {/* 回答内容 */}
      <div className="space-y-4">
        <div>
          <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
            资源标题：{answer.resource_title}
          </h4>
          <div className="flex items-center space-x-2 mb-2">
            <span className="px-2 py-0.5 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded text-xs font-medium">
              {answer.cloud_disk_type === "baidu"
                ? "百度网盘"
                : answer.cloud_disk_type === "aliyun"
                ? "阿里云盘"
                : answer.cloud_disk_type === "quark"
                ? "夸克网盘"
                : answer.cloud_disk_type === "xunlei"
                ? "迅雷网盘"
                : answer.cloud_disk_type}
            </span>
          </div>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
            <p className="text-sm text-gray-600 dark:text-gray-400 break-all">
              {answer.resource_link}
            </p>
          </div>
        </div>

        {answer.additional_info && (
          <div>
            <h5 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              附加信息：
            </h5>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {answer.additional_info}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function HelpRequestDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { showToast } = useToast();
  const [helpRequest, setHelpRequest] = useState<HelpRequestDetail | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadHelpRequestDetail = async () => {
      if (!params.id) return;

      setLoading(true);
      try {
        const response = await getHelpRequestDetail(params.id as string);
        if (response.status === "success") {
          setHelpRequest(response.data);
        } else {
          showToast(response.message || "获取求助详情失败", "error");
        }
      } catch (error) {
        console.error("获取求助详情失败:", error);
        showToast("获取求助详情失败", "error");
      } finally {
        setLoading(false);
      }
    };

    loadHelpRequestDetail();
  }, [params.id, showToast]);

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      </div>
    );
  }

  if (!helpRequest) {
    return (
      <div className="max-w-4xl mx-auto p-4">
        <div className="text-center py-12">
          <p className="text-secondary-text mb-4">求助不存在或已被删除</p>
          <Link
            href="/help-requests"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            返回求助列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      {/* 返回按钮 */}
      <div className="mb-6">
        <Link
          href="/help-requests"
          className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-1" />
          返回求助列表
        </Link>
      </div>

      {/* 求助详情卡片 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-6">
        {/* 标题和状态 */}
        <div className="flex items-start justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex-1 pr-4">
            {helpRequest.title}
          </h1>
          <span
            className={`px-3 py-1 text-sm font-medium text-white rounded ${getStatusColor(
              helpRequest.status
            )} whitespace-nowrap`}
          >
            {getStatusText(helpRequest.status)}
          </span>
        </div>

        {/* 标签 */}
        <div className="flex flex-wrap gap-2 mb-4">
          {helpRequest.resource_type && (
            <span className="px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 rounded text-sm font-medium">
              {helpRequest.resource_type === "movie"
                ? "电影"
                : helpRequest.resource_type === "tv"
                ? "电视剧"
                : helpRequest.resource_type === "music"
                ? "音乐"
                : helpRequest.resource_type === "software"
                ? "软件"
                : helpRequest.resource_type === "game"
                ? "游戏"
                : helpRequest.resource_type === "book"
                ? "书籍"
                : helpRequest.resource_type === "document"
                ? "文档"
                : helpRequest.resource_type === "other"
                ? "其他"
                : helpRequest.resource_type}
            </span>
          )}
          {helpRequest.cloud_disk_types?.map((diskType) => (
            <span
              key={diskType}
              className="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded text-sm font-medium"
            >
              {diskType === "baidu"
                ? "百度网盘"
                : diskType === "aliyun"
                ? "阿里云盘"
                : diskType === "quark"
                ? "夸克网盘"
                : diskType === "xunlei"
                ? "迅雷网盘"
                : diskType}
            </span>
          ))}
        </div>

        {/* 描述 */}
        {helpRequest.description && (
          <div className="mb-4">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              求助描述：
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {helpRequest.description}
            </p>
          </div>
        )}

        {/* 求助者信息和统计 */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
                <div>
                  <span className="font-medium text-blue-600 dark:text-blue-400">
                    {helpRequest.requester.nickname ||
                      helpRequest.requester.username}
                  </span>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {helpRequest.requester.points} 积分 ·{" "}
                    {helpRequest.requester.title || "新手"}
                  </div>
                </div>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                发布于 {formatDateTime(helpRequest.created_at)}
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              <span className="flex items-center">
                <EyeIcon className="w-4 h-4 mr-1" />
                {helpRequest.view_count}
              </span>
              <span className="flex items-center">
                <ChatBubbleLeftIcon className="w-4 h-4 mr-1" />
                {helpRequest.answer_count}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 回答列表 */}
      <div className="space-y-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
          回答 ({helpRequest.answers.length})
        </h2>

        {helpRequest.answers.length > 0 ? (
          helpRequest.answers.map((answer) => (
            <AnswerCard key={answer.id} answer={answer} />
          ))
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            <p className="text-gray-500 dark:text-gray-400">暂无回答</p>
          </div>
        )}
      </div>
    </div>
  );
}
